<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

return new class extends Migration
{
    /**
     * 完整的权限系统数据迁移
     * 
     * 补充所有缺失的权限数据，包括：
     * - 财务报表模块权限
     * - 门店管理模块权限
     * - 系统设置模块权限
     * - 数据统计模块权限
     * - 高级功能模块权限
     * - 数据导入导出权限
     */
    public function up(): void
    {
        $now = Carbon::now();
        
        // 批量插入完整的权限菜单数据
        DB::table('cs_menus')->insert([

            // =============================================================================
            // 财务报表模块权限（假设财务报表主菜单ID为3001）
            // =============================================================================
            [
                'pid' => 3001,
                'menu_name' => '查看报表',
                'menu_sign' => 'finance_report_view',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 3001,
                'menu_name' => '导出报表',
                'menu_sign' => 'finance_report_export',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 3001,
                'menu_name' => '打印报表',
                'menu_sign' => 'finance_report_print',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 3001,
                'menu_name' => '财务分析',
                'menu_sign' => 'finance_report_analysis',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 3001,
                'menu_name' => '自定义报表',
                'menu_sign' => 'finance_report_custom',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 5,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // =============================================================================
            // 门店管理模块权限（假设门店管理主菜单ID为1002）
            // =============================================================================
            [
                'pid' => 1002,
                'menu_name' => '新增门店',
                'menu_sign' => 'shop_add',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 1002,
                'menu_name' => '编辑门店',
                'menu_sign' => 'shop_edit',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 1002,
                'menu_name' => '删除门店',
                'menu_sign' => 'shop_delete',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 1002,
                'menu_name' => '门店状态',
                'menu_sign' => 'shop_status',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 1002,
                'menu_name' => '门店配置',
                'menu_sign' => 'shop_config',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 5,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 1002,
                'menu_name' => '门店数据统计',
                'menu_sign' => 'shop_statistics',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 6,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // =============================================================================
            // 系统设置模块权限（假设系统设置主菜单ID为9001）
            // =============================================================================
            [
                'pid' => 9001,
                'menu_name' => '基础配置',
                'menu_sign' => 'system_basic_config',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 9001,
                'menu_name' => '参数设置',
                'menu_sign' => 'system_parameter_config',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 9001,
                'menu_name' => '数据备份',
                'menu_sign' => 'system_data_backup',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 9001,
                'menu_name' => '数据恢复',
                'menu_sign' => 'system_data_restore',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 9001,
                'menu_name' => '系统清理',
                'menu_sign' => 'system_cleanup',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 5,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 9001,
                'menu_name' => '日志管理',
                'menu_sign' => 'system_log_management',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 6,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // =============================================================================
            // 数据统计模块权限（假设数据统计主菜单ID为3002）
            // =============================================================================
            [
                'pid' => 3002,
                'menu_name' => '销售统计',
                'menu_sign' => 'statistics_sales',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 3002,
                'menu_name' => '商品统计',
                'menu_sign' => 'statistics_goods',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 3002,
                'menu_name' => '会员统计',
                'menu_sign' => 'statistics_member',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 3002,
                'menu_name' => '营收统计',
                'menu_sign' => 'statistics_revenue',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 3002,
                'menu_name' => '趋势分析',
                'menu_sign' => 'statistics_trend_analysis',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 5,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 3002,
                'menu_name' => '对比分析',
                'menu_sign' => 'statistics_comparison',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 6,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // =============================================================================
            // 高级功能模块权限
            // =============================================================================
            
            // 数据导入导出（假设主菜单ID为9002）
            [
                'pid' => 9002,
                'menu_name' => '商品数据导入',
                'menu_sign' => 'data_import_goods',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 9002,
                'menu_name' => '会员数据导入',
                'menu_sign' => 'data_import_member',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 9002,
                'menu_name' => '订单数据导出',
                'menu_sign' => 'data_export_order',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 9002,
                'menu_name' => '财务数据导出',
                'menu_sign' => 'data_export_finance',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 9002,
                'menu_name' => '全量数据导出',
                'menu_sign' => 'data_export_full',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 5,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 批量操作权限（假设主菜单ID为9003）
            [
                'pid' => 9003,
                'menu_name' => '批量编辑商品',
                'menu_sign' => 'batch_edit_goods',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 9003,
                'menu_name' => '批量修改价格',
                'menu_sign' => 'batch_edit_price',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 9003,
                'menu_name' => '批量库存调整',
                'menu_sign' => 'batch_adjust_inventory',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 9003,
                'menu_name' => '批量会员操作',
                'menu_sign' => 'batch_operate_member',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 9003,
                'menu_name' => '批量状态修改',
                'menu_sign' => 'batch_modify_status',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 5,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // API管理权限（假设主菜单ID为9004）
            [
                'pid' => 9004,
                'menu_name' => 'API密钥管理',
                'menu_sign' => 'api_key_management',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 9004,
                'menu_name' => 'API访问日志',
                'menu_sign' => 'api_access_log',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 9004,
                'menu_name' => 'API限流配置',
                'menu_sign' => 'api_rate_limit_config',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 9004,
                'menu_name' => 'API文档管理',
                'menu_sign' => 'api_documentation',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 数据同步权限（假设主菜单ID为9005）
            [
                'pid' => 9005,
                'menu_name' => '门店数据同步',
                'menu_sign' => 'data_sync_shop',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 9005,
                'menu_name' => '库存数据同步',
                'menu_sign' => 'data_sync_inventory',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 9005,
                'menu_name' => '会员数据同步',
                'menu_sign' => 'data_sync_member',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 9005,
                'menu_name' => '同步状态监控',
                'menu_sign' => 'data_sync_monitor',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 9005,
                'menu_name' => '同步日志查看',
                'menu_sign' => 'data_sync_log',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 5,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // =============================================================================
            // 门店级别（shop level）的详细权限补充（menu_type = 2）
            // =============================================================================

            // 零售收银模块权限（假设主菜单ID为2001）
            [
                'pid' => 2001,
                'menu_name' => '开始收银',
                'menu_sign' => 'retail_cashier_start',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2001,
                'menu_name' => '商品扫码',
                'menu_sign' => 'retail_goods_scan',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2001,
                'menu_name' => '价格修改',
                'menu_sign' => 'retail_price_modify',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2001,
                'menu_name' => '折扣优惠',
                'menu_sign' => 'retail_discount_apply',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2001,
                'menu_name' => '挂单操作',
                'menu_sign' => 'retail_order_suspend',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 5,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2001,
                'menu_name' => '退货处理',
                'menu_sign' => 'retail_return_process',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 6,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 桌台业务模块权限（假设主菜单ID为2101）
            [
                'pid' => 2101,
                'menu_name' => '开台操作',
                'menu_sign' => 'table_open',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2101,
                'menu_name' => '加菜操作',
                'menu_sign' => 'table_add_dish',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2101,
                'menu_name' => '退菜操作',
                'menu_sign' => 'table_return_dish',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2101,
                'menu_name' => '转台操作',
                'menu_sign' => 'table_transfer',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2101,
                'menu_name' => '合台操作',
                'menu_sign' => 'table_merge',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 5,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2101,
                'menu_name' => '结账操作',
                'menu_sign' => 'table_checkout',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 6,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2101,
                'menu_name' => '桌台维护',
                'menu_sign' => 'table_maintenance',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 7,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 积分管理模块权限（假设积分获取规则菜单ID为2204，积分消费规则菜单ID为2205）
            [
                'pid' => 2204,
                'menu_name' => '新增规则',
                'menu_sign' => 'points_acquisition_add',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2204,
                'menu_name' => '编辑规则',
                'menu_sign' => 'points_acquisition_edit',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2204,
                'menu_name' => '删除规则',
                'menu_sign' => 'points_acquisition_delete',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2204,
                'menu_name' => '状态切换',
                'menu_sign' => 'points_acquisition_status',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            [
                'pid' => 2205,
                'menu_name' => '新增规则',
                'menu_sign' => 'points_deduction_add',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2205,
                'menu_name' => '编辑规则',
                'menu_sign' => 'points_deduction_edit',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2205,
                'menu_name' => '删除规则',
                'menu_sign' => 'points_deduction_delete',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2205,
                'menu_name' => '状态切换',
                'menu_sign' => 'points_deduction_status',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 消息模板管理权限（假设短信模板菜单ID为2702，系统消息菜单ID为2703）
            [
                'pid' => 2702,
                'menu_name' => '新增模板',
                'menu_sign' => 'sms_template_add',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2702,
                'menu_name' => '编辑模板',
                'menu_sign' => 'sms_template_edit',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2702,
                'menu_name' => '删除模板',
                'menu_sign' => 'sms_template_delete',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2702,
                'menu_name' => '模板测试',
                'menu_sign' => 'sms_template_test',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2702,
                'menu_name' => '状态切换',
                'menu_sign' => 'sms_template_status',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 5,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            [
                'pid' => 2703,
                'menu_name' => '新增消息',
                'menu_sign' => 'system_message_add',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2703,
                'menu_name' => '编辑消息',
                'menu_sign' => 'system_message_edit',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2703,
                'menu_name' => '删除消息',
                'menu_sign' => 'system_message_delete',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2703,
                'menu_name' => '消息推送',
                'menu_sign' => 'system_message_push',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2703,
                'menu_name' => '状态切换',
                'menu_sign' => 'system_message_status',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 5,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 库存预警管理权限（假设库存预警菜单ID为2604）
            [
                'pid' => 2604,
                'menu_name' => '新增预警',
                'menu_sign' => 'inventory_warning_add',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2604,
                'menu_name' => '编辑预警',
                'menu_sign' => 'inventory_warning_edit',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2604,
                'menu_name' => '删除预警',
                'menu_sign' => 'inventory_warning_delete',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2604,
                'menu_name' => '预警处理',
                'menu_sign' => 'inventory_warning_handle',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2604,
                'menu_name' => '状态切换',
                'menu_sign' => 'inventory_warning_status',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 5,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 消息设置权限（假设消息设置菜单ID为2704）
            [
                'pid' => 2704,
                'menu_name' => '编辑设置',
                'menu_sign' => 'message_setting_edit',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2704,
                'menu_name' => '推送设置',
                'menu_sign' => 'message_setting_push_config',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2704,
                'menu_name' => '通知设置',
                'menu_sign' => 'message_setting_notification',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 短信日志权限（假设短信日志菜单ID为2705）
            [
                'pid' => 2705,
                'menu_name' => '查看详情',
                'menu_sign' => 'sms_log_detail',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2705,
                'menu_name' => '重发短信',
                'menu_sign' => 'sms_log_resend',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 2705,
                'menu_name' => '导出日志',
                'menu_sign' => 'sms_log_export',
                'menu_type' => 2,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

        ]);

        // 添加数据库索引优化
        try {
            // 为cs_company_admin_roles表添加索引
            if (!$this->hasIndex('cs_company_admin_roles', 'idx_company_status')) {
                DB::statement('ALTER TABLE cs_company_admin_roles ADD INDEX idx_company_status (company_id, status)');
            }

            // 为cs_menus表添加复合索引
            if (!$this->hasIndex('cs_menus', 'idx_menu_type_status')) {
                DB::statement('ALTER TABLE cs_menus ADD INDEX idx_menu_type_status (menu_type, status, is_show)');
            }

            if (!$this->hasIndex('cs_menus', 'idx_pid_sort')) {
                DB::statement('ALTER TABLE cs_menus ADD INDEX idx_pid_sort (pid, sort)');
            }

            if (!$this->hasIndex('cs_menus', 'idx_menu_sign')) {
                DB::statement('ALTER TABLE cs_menus ADD INDEX idx_menu_sign (menu_sign)');
            }

        } catch (\Exception $e) {
            // 索引创建失败时记录但不影响迁移
            error_log('Failed to create indexes: ' . $e->getMessage());
        }
    }

    /**
     * 回滚迁移
     */
    public function down(): void
    {
        // 删除所有新增的权限菜单
        DB::table('cs_menus')->whereIn('menu_sign', [
            
            // 财务报表模块
            'finance_report_view', 'finance_report_export', 'finance_report_print', 
            'finance_report_analysis', 'finance_report_custom',
            
            // 门店管理模块
            'shop_add', 'shop_edit', 'shop_delete', 'shop_status', 'shop_config', 'shop_statistics',
            
            // 系统设置模块
            'system_basic_config', 'system_parameter_config', 'system_data_backup', 
            'system_data_restore', 'system_cleanup', 'system_log_management',
            
            // 数据统计模块
            'statistics_sales', 'statistics_goods', 'statistics_member', 
            'statistics_revenue', 'statistics_trend_analysis', 'statistics_comparison',
            
            // 数据导入导出
            'data_import_goods', 'data_import_member', 'data_export_order', 
            'data_export_finance', 'data_export_full',
            
            // 批量操作
            'batch_edit_goods', 'batch_edit_price', 'batch_adjust_inventory', 
            'batch_operate_member', 'batch_modify_status',
            
            // API管理
            'api_key_management', 'api_access_log', 'api_rate_limit_config', 'api_documentation',
            
            // 数据同步
            'data_sync_shop', 'data_sync_inventory', 'data_sync_member', 
            'data_sync_monitor', 'data_sync_log',
            
            // 零售收银模块
            'retail_cashier_start', 'retail_goods_scan', 'retail_price_modify', 
            'retail_discount_apply', 'retail_order_suspend', 'retail_return_process',
            
            // 桌台业务模块
            'table_open', 'table_add_dish', 'table_return_dish', 'table_transfer', 
            'table_merge', 'table_checkout', 'table_maintenance',
            
            // 积分管理模块
            'points_acquisition_add', 'points_acquisition_edit', 'points_acquisition_delete', 'points_acquisition_status',
            'points_deduction_add', 'points_deduction_edit', 'points_deduction_delete', 'points_deduction_status',
            
            // 消息模板管理
            'sms_template_add', 'sms_template_edit', 'sms_template_delete', 'sms_template_test', 'sms_template_status',
            'system_message_add', 'system_message_edit', 'system_message_delete', 'system_message_push', 'system_message_status',
            
            // 库存预警管理
            'inventory_warning_add', 'inventory_warning_edit', 'inventory_warning_delete', 
            'inventory_warning_handle', 'inventory_warning_status',
            
            // 消息设置
            'message_setting_edit', 'message_setting_push_config', 'message_setting_notification',
            
            // 短信日志
            'sms_log_detail', 'sms_log_resend', 'sms_log_export',
            
        ])->delete();

        // 移除索引（可选，因为可能影响其他功能）
        try {
            DB::statement('ALTER TABLE cs_company_admin_roles DROP INDEX IF EXISTS idx_company_status');
            DB::statement('ALTER TABLE cs_menus DROP INDEX IF EXISTS idx_menu_type_status');
            DB::statement('ALTER TABLE cs_menus DROP INDEX IF EXISTS idx_pid_sort');
            DB::statement('ALTER TABLE cs_menus DROP INDEX IF EXISTS idx_menu_sign');
        } catch (\Exception $e) {
            // 忽略索引删除错误
            error_log('Failed to drop indexes: ' . $e->getMessage());
        }
    }

    /**
     * 检查索引是否存在
     * 
     * @param string $table 表名
     * @param string $indexName 索引名
     * @return bool 是否存在
     */
    private function hasIndex(string $table, string $indexName): bool
    {
        try {
            $indexes = DB::select("SHOW INDEX FROM {$table} WHERE Key_name = ?", [$indexName]);
            return !empty($indexes);
        } catch (\Exception $e) {
            return false;
        }
    }
};