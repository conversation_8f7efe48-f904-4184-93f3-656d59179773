<?php

namespace app\controller;

use app\service\PermissionService;
use support\Request;
use support\Response;
use support\exception\BusinessException;
use support\Log;
use think\exception\ValidateException;

class CompanyBaseController extends BaseController
{
    /**
     * 权限服务实例
     */
    private ?PermissionService $permissionService = null;
    /**
     * 添加保存
     * @param Request $request
     * @return Response
     */
    public function addPost(Request $request): Response
    {
        try {
            $model = 'app\model\\' . $this->modelName;
            $data = $request->onlyPost($model::$validateFields);
            $data['company_id'] = $request->company_id;
            if (isset($data['id'])) {
                unset($data['id']);
            }
            $validateClass = 'app\validate\\' . $this->validateName;
            $validate = new $validateClass;
            if (!$validate->check($data)) {
                throw new ValidateException($validate->getError());
            }
            $result = $model::addPost($data);
            if (!empty($result['error'])) {
                throw new \Exception($result['msg']);
            }
            return success('添加成功');
        } catch (\Exception $e) {
            $errMsg = $e->getMessage();
        } catch (ValidateException $e) {
            $errMsg = $e->getError();
        }
        return fail($errMsg);
    }


    /**
     * 修改保存
     * @param Request $request
     * @return Response
     */
    public function editPost(Request $request): Response
    {
        try {
            $model = 'app\model\\' . $this->modelName;
            $data = $request->onlyPost($model::$validateFields);
            if (empty($data['id'])) {
                throw new \Exception('参数错误');
            }
            $data['company_id'] = $request->company_id;
            $validateClass = 'app\validate\\' . $this->validateName;
            $validate = new $validateClass;
            if (!$validate->check($data)) {
                throw new ValidateException($validate->getError());
            }
            $result = $model::editPost($data);
            if (!empty($result['error'])) {
                throw new \Exception($result['msg']);
            }
            return success('修改成功');
        } catch (\Exception $e) {
            $errMsg = $e->getMessage();
        } catch (ValidateException $e) {
            $errMsg = $e->getError();
        }
        return fail($errMsg);
    }

    /**
     * 检查用户权限
     * 
     * @param string $menuSign 权限标识
     * @return bool 是否有权限
     */
    protected function checkPermission(string $menuSign): bool
    {
        try {
            // 懒加载权限服务
            if ($this->permissionService === null) {
                $this->permissionService = new PermissionService();
            }

            $request = request();
            return $this->permissionService->checkPermission(
                $request->company_id ?? 0,
                $request->admin_id ?? 0,
                $request->role_id ?? 0,
                $menuSign
            );

        } catch (\Exception $e) {
            Log::info('CHECK_PERMISSION_CONTROLLER_ERROR', [
                'error' => $e->getMessage(),
                'menu_sign' => $menuSign,
                'company_id' => request()->company_id ?? 0,
                'admin_id' => request()->admin_id ?? 0
            ]);

            return false;
        }
    }

    /**
     * 要求用户必须有指定权限（无权限时抛出异常）
     * 
     * @param string $menuSign 权限标识
     * @throws BusinessException 权限不足异常
     */
    protected function requirePermission(string $menuSign): void
    {
        if (!$this->checkPermission($menuSign)) {
            $request = request();
            
            Log::info('PERMISSION_REQUIRED_FAILED', [
                'menu_sign' => $menuSign,
                'company_id' => $request->company_id ?? 0,
                'admin_id' => $request->admin_id ?? 0,
                'role_id' => $request->role_id ?? 0,
                'ip' => $request->getRealIp(),
                'controller' => $request->controller ?? '',
                'action' => $request->action ?? ''
            ]);

            throw new BusinessException('权限不足，无法执行该操作', 403);
        }
    }

    /**
     * 批量检查权限
     * 
     * @param array $menuSigns 权限标识数组
     * @return array 权限检查结果映射
     */
    protected function checkPermissions(array $menuSigns): array
    {
        try {
            // 懒加载权限服务
            if ($this->permissionService === null) {
                $this->permissionService = new PermissionService();
            }

            $request = request();
            return $this->permissionService->checkMultiplePermissions(
                $request->company_id ?? 0,
                $request->admin_id ?? 0,
                $request->role_id ?? 0,
                $menuSigns
            );

        } catch (\Exception $e) {
            Log::info('CHECK_PERMISSIONS_CONTROLLER_ERROR', [
                'error' => $e->getMessage(),
                'menu_signs' => $menuSigns,
                'company_id' => request()->company_id ?? 0,
                'admin_id' => request()->admin_id ?? 0
            ]);

            // 异常时全部返回false
            return array_fill_keys($menuSigns, false);
        }
    }

    /**
     * 获取用户权限列表
     * 
     * @return array 用户权限数据
     */
    protected function getUserPermissions(): array
    {
        try {
            // 懒加载权限服务
            if ($this->permissionService === null) {
                $this->permissionService = new PermissionService();
            }

            $request = request();
            return $this->permissionService->getUserPermissions(
                $request->company_id ?? 0,
                $request->admin_id ?? 0
            );

        } catch (\Exception $e) {
            Log::info('GET_USER_PERMISSIONS_CONTROLLER_ERROR', [
                'error' => $e->getMessage(),
                'company_id' => request()->company_id ?? 0,
                'admin_id' => request()->admin_id ?? 0
            ]);

            return ['menu_ids' => [], 'menu_signs' => []];
        }
    }

    /**
     * 获取用户可访问的菜单树
     * 
     * @param int $menuType 菜单类型（默认为1：公司级菜单）
     * @return array 菜单树数据
     */
    protected function getUserAccessibleMenus(int $menuType = 1): array
    {
        try {
            // 懒加载权限服务
            if ($this->permissionService === null) {
                $this->permissionService = new PermissionService();
            }

            $request = request();
            return $this->permissionService->getUserAccessibleMenus(
                $request->company_id ?? 0,
                $request->admin_id ?? 0,
                $request->role_id ?? 0,
                $menuType
            );

        } catch (\Exception $e) {
            Log::info('GET_USER_ACCESSIBLE_MENUS_CONTROLLER_ERROR', [
                'error' => $e->getMessage(),
                'menu_type' => $menuType,
                'company_id' => request()->company_id ?? 0,
                'admin_id' => request()->admin_id ?? 0
            ]);

            return [];
        }
    }

    /**
     * 检查是否为超级管理员
     * 
     * @return bool 是否为超级管理员
     */
    protected function isSuperAdmin(): bool
    {
        $request = request();
        return ($request->role_id ?? 0) === 1;
    }

    /**
     * 要求超级管理员权限
     * 
     * @throws BusinessException 权限不足异常
     */
    protected function requireSuperAdmin(): void
    {
        if (!$this->isSuperAdmin()) {
            $request = request();
            
            Log::info('SUPER_ADMIN_REQUIRED_FAILED', [
                'company_id' => $request->company_id ?? 0,
                'admin_id' => $request->admin_id ?? 0,
                'role_id' => $request->role_id ?? 0,
                'ip' => $request->getRealIp(),
                'controller' => $request->controller ?? '',
                'action' => $request->action ?? ''
            ]);

            throw new BusinessException('需要超级管理员权限', 403);
        }
    }

    /**
     * 清除权限缓存
     * 
     * @param int|null $adminId 用户ID（null表示清除当前用户）
     * @param int|null $roleId 角色ID（null表示不按角色清除）
     */
    protected function clearPermissionCache(?int $adminId = null, ?int $roleId = null): void
    {
        try {
            // 懒加载权限服务
            if ($this->permissionService === null) {
                $this->permissionService = new PermissionService();
            }

            $request = request();
            $companyId = $request->company_id ?? 0;
            $currentAdminId = $adminId ?? ($request->admin_id ?? 0);

            $this->permissionService->clearPermissionCache($companyId, $currentAdminId, $roleId);

        } catch (\Exception $e) {
            Log::info('CLEAR_PERMISSION_CACHE_CONTROLLER_ERROR', [
                'error' => $e->getMessage(),
                'admin_id' => $adminId,
                'role_id' => $roleId,
                'company_id' => request()->company_id ?? 0
            ]);
        }
    }

}
