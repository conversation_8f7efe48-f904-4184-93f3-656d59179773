<?php
namespace app\company\middleware;

use app\model\CsCompany;
use app\service\PermissionService;
use support\Redis;
use support\Log;
use Webman\MiddlewareInterface;
use Webman\Http\Response;
use Webman\Http\Request;

class Auth implements MiddlewareInterface
{
    /**
     * 权限服务实例
     */
    private ?PermissionService $permissionService = null;

    public function process(Request $request, callable $handler) : Response
    {
        $token = $request->header('token','');
        if (!in_array($request->action, $this->noNeedLogin()) && (empty($token) || !Redis::exists('company:' . $token))) {
            return fail('登录信息异常，请重新登录', 1003);
        }
        $request->admin_id = 0;
        $request->role_id = 0;
        $request->company_id = 0;
        if (!empty($token) && !in_array($request->action, $this->noNeedLogin())) {
            $adminRoleId = Redis::get('company:' . $token);
            $adminRoleId = explode('-',$adminRoleId);
            if (count($adminRoleId) != 3) {
                return fail('登录信息异常，请重新登录', 1003);
            }
            $request->admin_id = $adminRoleId[0];
            $request->role_id = $adminRoleId[1];
            $request->company_id = $adminRoleId[2];
            // 判断$company是否处于有效期内，如果是，将信息缓存一天，不再查询数据库
            $cacheKey = 'company_expired_at:' . $request->company_id;
            $expiredAt = Redis::get($cacheKey);
            if (empty($expiredAt)) {
                $expiredAt = CsCompany::where(['id' => $request->company_id])->value('expired_at');
                Redis::setex($cacheKey, 86400, $expiredAt);
            }
            if ($expiredAt < date('Y-m-d')) {
                return fail('公司授权已过期，请联系管理员', 1004);
            }

            // 新增：权限验证
            if (!$this->checkPermission($request)) {
                Log::info('PERMISSION_DENIED_ACCESS', [
                    'company_id' => $request->company_id,
                    'admin_id' => $request->admin_id,
                    'role_id' => $request->role_id,
                    'controller' => $request->controller ?? '',
                    'action' => $request->action ?? '',
                    'ip' => $request->getRealIp(),
                    'user_agent' => $request->header('user-agent', '')
                ]);
                return fail('权限不足，无法访问该功能', 403);
            }
        }
        // var_dump(Redis::keys('company:*'));
        return $handler($request);
    }

    /**
     * 权限检查
     * 
     * @param Request $request 请求对象
     * @return bool 是否有权限
     */
    private function checkPermission(Request $request): bool
    {
        try {
            // 懒加载权限服务
            if ($this->permissionService === null) {
                $this->permissionService = new PermissionService();
            }

            // 执行路由权限检查
            return $this->permissionService->checkRoutePermission($request);

        } catch (\Exception $e) {
            Log::info('PERMISSION_CHECK_MIDDLEWARE_ERROR', [
                'error' => $e->getMessage(),
                'company_id' => $request->company_id ?? 0,
                'admin_id' => $request->admin_id ?? 0,
                'controller' => $request->controller ?? '',
                'action' => $request->action ?? ''
            ]);

            // 权限检查异常时，为了安全考虑，拒绝访问
            return false;
        }
    }

    /**
     * 管理后台除了登录接口，其他接口都需要验证token，所以只验证action即可
     * @return string[]
     */
    protected function noNeedLogin(): array
    {
        return [
            'register',
            'login',
            'getLoginPage',
            'savePwd',
            'getCustomerServiceInfo',
            'sendForgotPasswordSms',
            'verifyForgotPasswordSms',
            'resetPassword',
            'resendForgotPasswordSms',
            'sendRegisterSms',
            'getUserAgreement',
            'getPrivacyPolicy'
        ];
    }

}
