<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="8f12913d-5559-41d4-84e4-874e9675365a" name="个人中心信息修改" comment="个人中心信息修改">
      <change beforePath="$PROJECT_DIR$/app/company/middleware/Auth.php" beforeDir="false" afterPath="$PROJECT_DIR$/app/company/middleware/Auth.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/controller/CompanyBaseController.php" beforeDir="false" afterPath="$PROJECT_DIR$/app/controller/CompanyBaseController.php" afterDir="false" />
    </list>
    <list id="0c9c1b3a-7c84-4d08-9db6-1752f0742336" name="安装webman/log监听未提交的事务" comment="安装webman/log监听未提交的事务" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerSettings" synchronizationState="DONT_SYNCHRONIZE">
    <pharConfigPath>$PROJECT_DIR$/composer.json</pharConfigPath>
    <execution />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="PhpDebugGeneral">
    <xdebug_debug_ports port="9003" />
  </component>
  <component name="PhpServers">
    <servers>
      <server host="127.0.0.1" id="559b0dd3-e6af-4c4d-a967-570b29b3efe3" name="webman" port="8787" />
    </servers>
  </component>
  <component name="PhpWebServerValidation" path_to_validation_script="E:\phpstudy_pro\WWW\2024\shop-sass-xly\sass-api\app\Webman" selected_validation_type="LOCAL" web_path_to_validation_script="http://127.0.0.1:8787/admin/CsCompany/index?page=1&amp;pageSize=10" />
  <component name="PhpWorkspaceProjectConfiguration" interpreter_name="PHP 8.2">
    <include_path>
      <path value="$PROJECT_DIR$/vendor/workerman/crontab" />
      <path value="$PROJECT_DIR$/vendor/workerman/webman-framework" />
      <path value="$PROJECT_DIR$/vendor/workerman/workerman" />
      <path value="$PROJECT_DIR$/vendor/opis/closure" />
      <path value="$PROJECT_DIR$/vendor/phpoption/phpoption" />
      <path value="$PROJECT_DIR$/vendor/voku/portable-ascii" />
      <path value="$PROJECT_DIR$/vendor/webman/event" />
      <path value="$PROJECT_DIR$/vendor/monolog/monolog" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php80" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/vendor/symfony/translation" />
      <path value="$PROJECT_DIR$/vendor/symfony/translation-contracts" />
      <path value="$PROJECT_DIR$/vendor/vlucas/phpdotenv" />
      <path value="$PROJECT_DIR$/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/vendor/lcobucci/jwt" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/vendor/webman/console" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/vendor/symfony/var-dumper" />
      <path value="$PROJECT_DIR$/vendor/composer" />
      <path value="$PROJECT_DIR$/vendor/brick/math" />
      <path value="$PROJECT_DIR$/vendor/symfony/console" />
      <path value="$PROJECT_DIR$/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/vendor/symfony/service-contracts" />
      <path value="$PROJECT_DIR$/vendor/psr/log" />
      <path value="$PROJECT_DIR$/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/vendor/doctrine/inflector" />
      <path value="$PROJECT_DIR$/vendor/doctrine/lexer" />
      <path value="$PROJECT_DIR$/vendor/carbonphp/carbon-doctrine-types" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-normalizer" />
      <path value="$PROJECT_DIR$/vendor/psr/cache" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/vendor/psr/container" />
      <path value="$PROJECT_DIR$/vendor/doctrine/annotations" />
      <path value="$PROJECT_DIR$/vendor/illuminate/contracts" />
      <path value="$PROJECT_DIR$/vendor/nikic/fast-route" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-container" />
      <path value="$PROJECT_DIR$/vendor/illuminate/database" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-helper" />
      <path value="$PROJECT_DIR$/vendor/illuminate/events" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-validate" />
      <path value="$PROJECT_DIR$/vendor/illuminate/macroable" />
      <path value="$PROJECT_DIR$/vendor/illuminate/bus" />
      <path value="$PROJECT_DIR$/vendor/illuminate/collections" />
      <path value="$PROJECT_DIR$/vendor/illuminate/conditionable" />
      <path value="$PROJECT_DIR$/vendor/illuminate/container" />
      <path value="$PROJECT_DIR$/vendor/sqids/sqids" />
      <path value="$PROJECT_DIR$/vendor/intervention/image" />
      <path value="$PROJECT_DIR$/vendor/graham-campbell/result-type" />
      <path value="$PROJECT_DIR$/vendor/illuminate/pagination" />
      <path value="$PROJECT_DIR$/vendor/illuminate/pipeline" />
      <path value="$PROJECT_DIR$/vendor/illuminate/support" />
      <path value="$PROJECT_DIR$/vendor/intervention/gif" />
      <path value="$PROJECT_DIR$/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/rate-limiter" />
      <path value="$PROJECT_DIR$/vendor/php-di/invoker" />
      <path value="$PROJECT_DIR$/vendor/php-di/php-di" />
      <path value="$PROJECT_DIR$/vendor/symfony/options-resolver" />
      <path value="$PROJECT_DIR$/vendor/php-di/phpdoc-reader" />
      <path value="$PROJECT_DIR$/vendor/laravel/serializable-closure" />
      <path value="$PROJECT_DIR$/vendor/nesbot/carbon" />
      <path value="$PROJECT_DIR$/vendor/illuminate/redis" />
      <path value="$PROJECT_DIR$/vendor/webman/log" />
      <path value="$PROJECT_DIR$/vendor/workerman/coroutine" />
      <path value="$PROJECT_DIR$/vendor/webman/database" />
      <path value="$PROJECT_DIR$/vendor/webman/redis" />
      <path value="$PROJECT_DIR$/vendor/alibabacloud/openapi-core" />
      <path value="$PROJECT_DIR$/vendor/alibabacloud/gateway-spi" />
      <path value="$PROJECT_DIR$/vendor/alibabacloud/dysmsapi-20170525" />
      <path value="$PROJECT_DIR$/vendor/alibabacloud/darabonba" />
      <path value="$PROJECT_DIR$/vendor/alibabacloud/credentials" />
      <path value="$PROJECT_DIR$/vendor/adbario/php-dot-notation" />
      <path value="$PROJECT_DIR$/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/vendor/alibabacloud/tea" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/vendor/bacon/bacon-qr-code" />
      <path value="$PROJECT_DIR$/vendor/dasprid/enum" />
      <path value="$PROJECT_DIR$/vendor/endroid/qr-code" />
      <path value="$PROJECT_DIR$/vendor/nyholm/psr7" />
      <path value="$PROJECT_DIR$/vendor/nyholm/psr7-server" />
      <path value="$PROJECT_DIR$/vendor/overtrue/socialite" />
      <path value="$PROJECT_DIR$/vendor/symfony/mime" />
      <path value="$PROJECT_DIR$/vendor/w7corp/easywechat" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-idn" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-client-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php81" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-foundation" />
      <path value="$PROJECT_DIR$/vendor/symfony/var-exporter" />
      <path value="$PROJECT_DIR$/vendor/symfony/cache-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/cache" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-client" />
      <path value="$PROJECT_DIR$/vendor/symfony/psr-http-message-bridge" />
      <path value="$PROJECT_DIR$/vendor/thenorthmemory/xml" />
    </include_path>
  </component>
  <component name="PhpXdebugProxy" ide_key="PHPSTORM" host="127.0.0.1" port="9003" />
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2mgJ8g4YGGCPTkf26JpXd3FirSk" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;PHP HTTP 请求.测试.executor&quot;: &quot;Debug&quot;,
    &quot;PHP 脚本.BaseModel.php.executor&quot;: &quot;Debug&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/phpstudy_pro/WWW/2024/shop-sass-xly/sass-api/app/Webman/app/shop&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\phpstudy_pro\WWW\2024\shop-sass-xly\sass-api\app\Webman\app\shop" />
      <recent name="E:\phpstudy_pro\WWW\2024\shop-sass-xly\sass-api\app\Webman\vendor\webman\console\src\Commands" />
      <recent name="E:\phpstudy_pro\WWW\2024\shop-sass-xly\sass-api\app\Webman\app\service" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="测试" type="PhpHttpRequestRunConfigurationType" factoryName="PHP HTTP Request" server_name="webman" url="/admin/CsCompany/index">
      <HttpRequestSettings query_string="page=1&amp;pageSize=10" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-php-predefined-ba97393d7c68-b4dcf6bb9de9-com.jetbrains.php.sharedIndexes-PS-233.13135.108" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="8f12913d-5559-41d4-84e4-874e9675365a" name="更改" comment="" />
      <changelist id="0c9c1b3a-7c84-4d08-9db6-1752f0742336" name="运行异常日志记录" comment="运行异常日志记录" />
      <created>1727492127965</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1727492127965</updated>
      <workItem from="1727492129331" duration="142000" />
      <workItem from="1727492278849" duration="3185000" />
      <workItem from="1730462559873" duration="844000" />
      <workItem from="1730464315371" duration="6020000" />
      <workItem from="1730505584428" duration="3770000" />
      <workItem from="1730512926577" duration="6345000" />
      <workItem from="1730528272288" duration="29671000" />
      <workItem from="1730591220416" duration="19149000" />
      <workItem from="1730621424009" duration="12188000" />
      <workItem from="1730680497873" duration="12456000" />
      <workItem from="1730713920655" duration="11779000" />
      <workItem from="1730728997160" duration="70000" />
      <workItem from="1730768003864" duration="1284000" />
      <workItem from="1730774661309" duration="8004000" />
      <workItem from="1730792582282" duration="5622000" />
      <workItem from="1730811185039" duration="2683000" />
      <workItem from="1730856357581" duration="7726000" />
      <workItem from="1730879599322" duration="1237000" />
      <workItem from="1730884210453" duration="6950000" />
      <workItem from="1730943194384" duration="706000" />
      <workItem from="1730964366250" duration="674000" />
      <workItem from="1731032910920" duration="1943000" />
      <workItem from="1731119349847" duration="610000" />
      <workItem from="1731140073874" duration="779000" />
      <workItem from="1731141342461" duration="2995000" />
      <workItem from="1731148912865" duration="7882000" />
      <workItem from="1731196612534" duration="8000" />
      <workItem from="1731196649374" duration="652000" />
      <workItem from="1731220468834" duration="601000" />
      <workItem from="1731928576674" duration="617000" />
      <workItem from="1731980510070" duration="3097000" />
      <workItem from="1732000578473" duration="1416000" />
      <workItem from="1732106074024" duration="3281000" />
      <workItem from="1732174204300" duration="2733000" />
      <workItem from="1732417654038" duration="955000" />
      <workItem from="1732434341313" duration="1036000" />
      <workItem from="1732609240852" duration="21000" />
      <workItem from="1734956579410" duration="1210000" />
      <workItem from="1735005155142" duration="815000" />
      <workItem from="1735030883606" duration="815000" />
      <workItem from="1735034176485" duration="4827000" />
      <workItem from="1735084794931" duration="1253000" />
      <workItem from="1735127840105" duration="619000" />
      <workItem from="1735176311552" duration="2891000" />
      <workItem from="1735204562236" duration="8818000" />
      <workItem from="1735264774760" duration="2462000" />
      <workItem from="1735291233679" duration="63000" />
      <workItem from="1737856995302" duration="8201000" />
      <workItem from="1737880916648" duration="6270000" />
      <workItem from="1738290254436" duration="76000" />
      <workItem from="1738407920700" duration="2028000" />
      <workItem from="1738483162540" duration="8165000" />
      <workItem from="1738497539186" duration="761000" />
      <workItem from="1738500774756" duration="2755000" />
      <workItem from="1738548423550" duration="11823000" />
      <workItem from="1738634913087" duration="930000" />
      <workItem from="1738659436637" duration="3267000" />
      <workItem from="1738735406414" duration="7853000" />
      <workItem from="1739066703624" duration="8148000" />
      <workItem from="1739147776083" duration="16860000" />
      <workItem from="1740010953785" duration="1880000" />
      <workItem from="1740022830257" duration="977000" />
      <workItem from="1740036716738" duration="1797000" />
      <workItem from="1740278743280" duration="620000" />
      <workItem from="1740365811474" duration="1978000" />
      <workItem from="1741574735466" duration="2417000" />
      <workItem from="1741664164387" duration="695000" />
      <workItem from="1742115943632" duration="8389000" />
      <workItem from="1742170085519" duration="10044000" />
      <workItem from="1742192532661" duration="4750000" />
      <workItem from="1742255942643" duration="3800000" />
      <workItem from="1742350548306" duration="601000" />
      <workItem from="1742515018685" duration="612000" />
      <workItem from="1742629226789" duration="10831000" />
      <workItem from="1742890208265" duration="4773000" />
      <workItem from="1742955486186" duration="1311000" />
      <workItem from="1742981458881" duration="4334000" />
      <workItem from="1743032589664" duration="2501000" />
      <workItem from="1743057895467" duration="4982000" />
      <workItem from="1743118541183" duration="609000" />
      <workItem from="1743229816252" duration="4658000" />
      <workItem from="1743383525075" duration="2495000" />
      <workItem from="1743584026412" duration="6257000" />
      <workItem from="1743641026825" duration="2017000" />
      <workItem from="1743906123539" duration="2859000" />
      <workItem from="1743929596352" duration="5564000" />
      <workItem from="1743987307781" duration="3196000" />
      <workItem from="1744013907715" duration="2598000" />
      <workItem from="1744098672365" duration="1895000" />
      <workItem from="1744154173052" duration="609000" />
      <workItem from="1744188561683" duration="1960000" />
      <workItem from="1744197906801" duration="7246000" />
      <workItem from="1744245887879" duration="747000" />
      <workItem from="1744270452985" duration="1221000" />
      <workItem from="1744336194391" duration="607000" />
      <workItem from="1744358333104" duration="4280000" />
      <workItem from="1744367173925" duration="422000" />
      <workItem from="1744433313709" duration="126000" />
      <workItem from="1744448487797" duration="21000" />
      <workItem from="1744455830248" duration="674000" />
      <workItem from="1744764951365" duration="5382000" />
      <workItem from="1745113505713" duration="1059000" />
      <workItem from="1745311935379" duration="8931000" />
      <workItem from="1746746539130" duration="605000" />
      <workItem from="1747126407806" duration="15000" />
      <workItem from="1747207260479" duration="19000" />
      <workItem from="1747303269137" duration="2729000" />
      <workItem from="1747615528323" duration="814000" />
      <workItem from="1747876376746" duration="65000" />
      <workItem from="1748590637008" duration="1378000" />
      <workItem from="1748915060098" duration="1995000" />
      <workItem from="1749523976747" duration="836000" />
      <workItem from="1750041301023" duration="349000" />
      <workItem from="1750062009471" duration="612000" />
      <workItem from="1750408927317" duration="169000" />
      <workItem from="1750475731881" duration="1411000" />
      <workItem from="1750501958322" duration="2741000" />
      <workItem from="1750579048696" duration="3623000" />
      <workItem from="1750662476332" duration="1800000" />
      <workItem from="1750734286488" duration="2242000" />
      <workItem from="1750753364998" duration="1226000" />
      <workItem from="1750899509689" duration="1641000" />
      <workItem from="1751012517841" duration="2043000" />
      <workItem from="1751030217341" duration="1893000" />
      <workItem from="1751101996999" duration="8448000" />
      <workItem from="1751156429682" duration="5996000" />
      <workItem from="1751189594378" duration="569000" />
      <workItem from="1751258948252" duration="1730000" />
      <workItem from="1751335384661" duration="805000" />
      <workItem from="1751417343737" duration="3639000" />
      <workItem from="1751444985751" duration="6214000" />
      <workItem from="1751501763707" duration="3125000" />
      <workItem from="1751524885652" duration="3817000" />
      <workItem from="1751592952545" duration="4840000" />
      <workItem from="1751618964470" duration="2069000" />
      <workItem from="1751678171954" duration="599000" />
      <workItem from="1751686536261" duration="1904000" />
      <workItem from="1751702860856" duration="3918000" />
      <workItem from="1751724558609" duration="3357000" />
      <workItem from="1751794052449" duration="1212000" />
      <workItem from="1751849301395" duration="638000" />
      <workItem from="1752139105665" duration="390000" />
      <workItem from="1752287945305" duration="513000" />
      <workItem from="1752288478240" duration="2623000" />
      <workItem from="1752832664991" duration="800000" />
      <workItem from="1752913028151" duration="1492000" />
      <workItem from="1753165975091" duration="2560000" />
      <workItem from="1753178564997" duration="2020000" />
      <workItem from="1753231331509" duration="1699000" />
      <workItem from="1753337206292" duration="398000" />
      <workItem from="1753416500577" duration="855000" />
      <workItem from="1753434134610" duration="1539000" />
      <workItem from="1753442637518" duration="1244000" />
      <workItem from="1753534515425" duration="598000" />
      <workItem from="1753618621698" duration="599000" />
      <workItem from="1753664243718" duration="3154000" />
      <workItem from="1754009406368" duration="3044000" />
      <workItem from="1754035197923" duration="2975000" />
      <workItem from="1754049262426" duration="3667000" />
      <workItem from="1754092631319" duration="2378000" />
      <workItem from="1754128476272" duration="1886000" />
      <workItem from="1754138295926" duration="4198000" />
    </task>
    <task id="LOCAL-00038" summary="杂项修改">
      <option name="closed" value="true" />
      <created>1743598312078</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1743598312078</updated>
    </task>
    <task id="LOCAL-00039" summary="修改账号资料">
      <option name="closed" value="true" />
      <created>1743910133201</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1743910133201</updated>
    </task>
    <task id="LOCAL-00040" summary="商品规则及商品分类">
      <option name="closed" value="true" />
      <created>1743930147955</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1743930147955</updated>
    </task>
    <task id="LOCAL-00041" summary="商品管理">
      <option name="closed" value="true" />
      <created>1743934558921</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1743934558921</updated>
    </task>
    <task id="LOCAL-00042" summary="商品备注标签管理">
      <option name="closed" value="true" />
      <created>1743994617754</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1743994617754</updated>
    </task>
    <task id="LOCAL-00043" summary="配品及配品分类">
      <option name="closed" value="true" />
      <created>1743995857519</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1743995857519</updated>
    </task>
    <task id="LOCAL-00044" summary="收款方式">
      <option name="closed" value="true" />
      <created>1744019685825</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1744019685825</updated>
    </task>
    <task id="LOCAL-00045" summary="授权端接口更新">
      <option name="closed" value="true" />
      <created>1744770560885</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1744770560885</updated>
    </task>
    <task id="LOCAL-00046" summary="授权端接口更新">
      <option name="closed" value="true" />
      <created>1745322176948</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1745322176948</updated>
    </task>
    <task id="LOCAL-00047" summary="桌台费取消包段计费规则">
      <option name="closed" value="true" />
      <created>1750475790201</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>1750475790201</updated>
    </task>
    <task id="LOCAL-00048" summary="优化店铺初始化数据">
      <option name="closed" value="true" />
      <created>1750501986168</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>1750501986168</updated>
    </task>
    <task id="LOCAL-00049" summary="优化店铺初始化数据">
      <option name="closed" value="true" />
      <created>1750503829559</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1750503829559</updated>
    </task>
    <task id="LOCAL-00050" summary="修复请求数组数据验证">
      <option name="closed" value="true" />
      <created>1750579103864</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>1750579103864</updated>
    </task>
    <task id="LOCAL-00051" summary="去除打印信息">
      <option name="closed" value="true" />
      <created>1750753392331</created>
      <option name="number" value="00051" />
      <option name="presentableId" value="LOCAL-00051" />
      <option name="project" value="LOCAL" />
      <updated>1750753392332</updated>
    </task>
    <task id="LOCAL-00052" summary="优化消息管理">
      <option name="closed" value="true" />
      <created>1750899618256</created>
      <option name="number" value="00052" />
      <option name="presentableId" value="LOCAL-00052" />
      <option name="project" value="LOCAL" />
      <updated>1750899618257</updated>
    </task>
    <task id="LOCAL-00053" summary="修复值为0的搜索忽略问题">
      <option name="closed" value="true" />
      <created>1750910579676</created>
      <option name="number" value="00053" />
      <option name="presentableId" value="LOCAL-00053" />
      <option name="project" value="LOCAL" />
      <updated>1750910579676</updated>
    </task>
    <task id="LOCAL-00054" summary="打印设置">
      <option name="closed" value="true" />
      <created>1751016837544</created>
      <option name="number" value="00054" />
      <option name="presentableId" value="LOCAL-00054" />
      <option name="project" value="LOCAL" />
      <updated>1751016837544</updated>
    </task>
    <task id="LOCAL-00055" summary="打印设置">
      <option name="closed" value="true" />
      <created>1751033711912</created>
      <option name="number" value="00055" />
      <option name="presentableId" value="LOCAL-00055" />
      <option name="project" value="LOCAL" />
      <updated>1751033711912</updated>
    </task>
    <task id="LOCAL-00056" summary="优化利润分析">
      <option name="closed" value="true" />
      <created>1751103358362</created>
      <option name="number" value="00056" />
      <option name="presentableId" value="LOCAL-00056" />
      <option name="project" value="LOCAL" />
      <updated>1751103358363</updated>
    </task>
    <task id="LOCAL-00057" summary="优化利润分析">
      <option name="closed" value="true" />
      <created>1751103563382</created>
      <option name="number" value="00057" />
      <option name="presentableId" value="LOCAL-00057" />
      <option name="project" value="LOCAL" />
      <updated>1751103563382</updated>
    </task>
    <task id="LOCAL-00058" summary="优化库存管理">
      <option name="closed" value="true" />
      <created>1751105473168</created>
      <option name="number" value="00058" />
      <option name="presentableId" value="LOCAL-00058" />
      <option name="project" value="LOCAL" />
      <updated>1751105473168</updated>
    </task>
    <task id="LOCAL-00059" summary="优化库存概览商品名搜索没限定门店bug">
      <option name="closed" value="true" />
      <created>1751106401642</created>
      <option name="number" value="00059" />
      <option name="presentableId" value="LOCAL-00059" />
      <option name="project" value="LOCAL" />
      <updated>1751106401642</updated>
    </task>
    <task id="LOCAL-00060" summary="修复调整库存未更新商品表的bug">
      <option name="closed" value="true" />
      <created>1751107007137</created>
      <option name="number" value="00060" />
      <option name="presentableId" value="LOCAL-00060" />
      <option name="project" value="LOCAL" />
      <updated>1751107007137</updated>
    </task>
    <task id="LOCAL-00061" summary="增加商品类型">
      <option name="closed" value="true" />
      <created>1751122215162</created>
      <option name="number" value="00061" />
      <option name="presentableId" value="LOCAL-00061" />
      <option name="project" value="LOCAL" />
      <updated>1751122215162</updated>
    </task>
    <task id="LOCAL-00062" summary="修复总成bug">
      <option name="closed" value="true" />
      <created>1751124435355</created>
      <option name="number" value="00062" />
      <option name="presentableId" value="LOCAL-00062" />
      <option name="project" value="LOCAL" />
      <updated>1751124435355</updated>
    </task>
    <task id="LOCAL-00063" summary="修复总成bug">
      <option name="closed" value="true" />
      <created>1751156537670</created>
      <option name="number" value="00063" />
      <option name="presentableId" value="LOCAL-00063" />
      <option name="project" value="LOCAL" />
      <updated>1751156537670</updated>
    </task>
    <task id="LOCAL-00064" summary="库存记录添加关联ID">
      <option name="closed" value="true" />
      <created>1751157924618</created>
      <option name="number" value="00064" />
      <option name="presentableId" value="LOCAL-00064" />
      <option name="project" value="LOCAL" />
      <updated>1751157924619</updated>
    </task>
    <task id="LOCAL-00065" summary="库存盘点功能完成">
      <option name="closed" value="true" />
      <created>1751189679216</created>
      <option name="number" value="00065" />
      <option name="presentableId" value="LOCAL-00065" />
      <option name="project" value="LOCAL" />
      <updated>1751189679216</updated>
    </task>
    <task id="LOCAL-00066" summary="修复事务返回结果兼容问题">
      <option name="closed" value="true" />
      <created>1751335770377</created>
      <option name="number" value="00066" />
      <option name="presentableId" value="LOCAL-00066" />
      <option name="project" value="LOCAL" />
      <updated>1751335770377</updated>
    </task>
    <task id="LOCAL-00067" summary="修复事务返回结果兼容问题">
      <option name="closed" value="true" />
      <created>1751335836557</created>
      <option name="number" value="00067" />
      <option name="presentableId" value="LOCAL-00067" />
      <option name="project" value="LOCAL" />
      <updated>1751335836557</updated>
    </task>
    <task id="LOCAL-00068" summary="修复事务返回结果兼容问题">
      <option name="closed" value="true" />
      <created>1751335913449</created>
      <option name="number" value="00068" />
      <option name="presentableId" value="LOCAL-00068" />
      <option name="project" value="LOCAL" />
      <updated>1751335913449</updated>
    </task>
    <task id="LOCAL-00069" summary="支付方式返回逻辑修改">
      <option name="closed" value="true" />
      <created>1751417375075</created>
      <option name="number" value="00069" />
      <option name="presentableId" value="LOCAL-00069" />
      <option name="project" value="LOCAL" />
      <updated>1751417375075</updated>
    </task>
    <task id="LOCAL-00070" summary="结账后生成打印数据">
      <option name="closed" value="true" />
      <created>1751453930349</created>
      <option name="number" value="00070" />
      <option name="presentableId" value="LOCAL-00070" />
      <option name="project" value="LOCAL" />
      <updated>1751453930350</updated>
    </task>
    <task id="LOCAL-00071" summary="修复success函数参数问题">
      <option name="closed" value="true" />
      <created>1751454930497</created>
      <option name="number" value="00071" />
      <option name="presentableId" value="LOCAL-00071" />
      <option name="project" value="LOCAL" />
      <updated>1751454930497</updated>
    </task>
    <task id="LOCAL-00072" summary="初始化菜单">
      <option name="closed" value="true" />
      <created>1751527488432</created>
      <option name="number" value="00072" />
      <option name="presentableId" value="LOCAL-00072" />
      <option name="project" value="LOCAL" />
      <updated>1751527488433</updated>
    </task>
    <task id="LOCAL-00073" summary="增加班选中状态">
      <option name="closed" value="true" />
      <created>1751604253970</created>
      <option name="number" value="00073" />
      <option name="presentableId" value="LOCAL-00073" />
      <option name="project" value="LOCAL" />
      <updated>1751604253970</updated>
    </task>
    <task id="LOCAL-00074" summary="隐藏门店菜单设置逻辑">
      <option name="closed" value="true" />
      <created>1751623533197</created>
      <option name="number" value="00074" />
      <option name="presentableId" value="LOCAL-00074" />
      <option name="project" value="LOCAL" />
      <updated>1751623533198</updated>
    </task>
    <task id="LOCAL-00075" summary="取消字段验证">
      <option name="closed" value="true" />
      <created>1751714463673</created>
      <option name="number" value="00075" />
      <option name="presentableId" value="LOCAL-00075" />
      <option name="project" value="LOCAL" />
      <updated>1751714463673</updated>
    </task>
    <task id="LOCAL-00076" summary="预订管理增加客户搜索">
      <option name="closed" value="true" />
      <created>1751715827632</created>
      <option name="number" value="00076" />
      <option name="presentableId" value="LOCAL-00076" />
      <option name="project" value="LOCAL" />
      <updated>1751715827632</updated>
    </task>
    <task id="LOCAL-00077" summary="安装扩展，生成桌台二维码">
      <option name="closed" value="true" />
      <created>1751728470717</created>
      <option name="number" value="00077" />
      <option name="presentableId" value="LOCAL-00077" />
      <option name="project" value="LOCAL" />
      <updated>1751728470718</updated>
    </task>
    <task id="LOCAL-00078" summary="解决生成二维码中文乱码问题">
      <option name="closed" value="true" />
      <created>1751729860744</created>
      <option name="number" value="00078" />
      <option name="presentableId" value="LOCAL-00078" />
      <option name="project" value="LOCAL" />
      <updated>1751729860745</updated>
    </task>
    <task id="LOCAL-00079" summary="解决生成二维码中文乱码问题">
      <option name="closed" value="true" />
      <created>1753166059211</created>
      <option name="number" value="00079" />
      <option name="presentableId" value="LOCAL-00079" />
      <option name="project" value="LOCAL" />
      <updated>1753166059211</updated>
    </task>
    <task id="LOCAL-00080" summary="增加预订“确认”和”拒绝“接口">
      <option name="closed" value="true" />
      <created>1753168367087</created>
      <option name="number" value="00080" />
      <option name="presentableId" value="LOCAL-00080" />
      <option name="project" value="LOCAL" />
      <updated>1753168367087</updated>
    </task>
    <task id="LOCAL-00081" summary="初始化门店时增加无服务费和无桌台费规则">
      <option name="closed" value="true" />
      <created>1753416554092</created>
      <option name="number" value="00081" />
      <option name="presentableId" value="LOCAL-00081" />
      <option name="project" value="LOCAL" />
      <updated>1753416554092</updated>
    </task>
    <task id="LOCAL-00082" summary="初始化门店时增加无服务费和无桌台费规则">
      <option name="closed" value="true" />
      <created>1753416699430</created>
      <option name="number" value="00082" />
      <option name="presentableId" value="LOCAL-00082" />
      <option name="project" value="LOCAL" />
      <updated>1753416699430</updated>
    </task>
    <task id="LOCAL-00083" summary="企业管理系统登录接口">
      <option name="closed" value="true" />
      <created>1754018487713</created>
      <option name="number" value="00083" />
      <option name="presentableId" value="LOCAL-00083" />
      <option name="project" value="LOCAL" />
      <updated>1754018487713</updated>
    </task>
    <task id="LOCAL-00084" summary="用户协议和隐私政策">
      <option name="closed" value="true" />
      <created>1754035319697</created>
      <option name="number" value="00084" />
      <option name="presentableId" value="LOCAL-00084" />
      <option name="project" value="LOCAL" />
      <updated>1754035319697</updated>
    </task>
    <task id="LOCAL-00085" summary="企业用户判断公司过期状态">
      <option name="closed" value="true" />
      <created>1754102948857</created>
      <option name="number" value="00085" />
      <option name="presentableId" value="LOCAL-00085" />
      <option name="project" value="LOCAL" />
      <updated>1754102948858</updated>
    </task>
    <task id="LOCAL-00086" summary="个人中心信息修改">
      <option name="closed" value="true" />
      <created>1754139535203</created>
      <option name="number" value="00086" />
      <option name="presentableId" value="LOCAL-00086" />
      <option name="project" value="LOCAL" />
      <updated>1754139535204</updated>
    </task>
    <option name="localTasksCounter" value="87" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="优化库存管理" />
    <MESSAGE value="优化库存概览商品名搜索没限定门店bug" />
    <MESSAGE value="修复调整库存未更新商品表的bug" />
    <MESSAGE value="增加商品类型" />
    <MESSAGE value="修复总成bug" />
    <MESSAGE value="库存记录添加关联ID" />
    <MESSAGE value="库存盘点功能完成" />
    <MESSAGE value="修复事务返回结果兼容问题" />
    <MESSAGE value="支付方式返回逻辑修改" />
    <MESSAGE value="结账后生成打印数据" />
    <MESSAGE value="修复success函数参数问题" />
    <MESSAGE value="初始化菜单" />
    <MESSAGE value="增加班选中状态" />
    <MESSAGE value="隐藏门店菜单设置逻辑" />
    <MESSAGE value="取消字段验证" />
    <MESSAGE value="预订管理增加客户搜索" />
    <MESSAGE value="安装扩展，生成桌台二维码" />
    <MESSAGE value="解决生成二维码中文乱码问题" />
    <MESSAGE value="增加预订“确认”和”拒绝“接口" />
    <MESSAGE value="初始化门店时增加无服务费和无桌台费规则" />
    <MESSAGE value="企业管理系统登录接口" />
    <MESSAGE value="用户协议和隐私政策" />
    <MESSAGE value="企业用户判断公司过期状态" />
    <MESSAGE value="企业管理系统数据大屏" />
    <MESSAGE value="个人中心信息修改" />
    <option name="LAST_COMMIT_MESSAGE" value="个人中心信息修改" />
  </component>
</project>