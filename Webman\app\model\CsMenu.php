<?php

namespace app\model;

use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * cs_menus 系统菜单表
 * @property integer $id (主键)
 * @property integer $pid 上级菜单主键
 * @property string $menu_name 菜单名称
 * @property string $menu_img 菜单图标
 * @property string $menu_sign 菜单标识
 * @property integer $menu_type 菜单类型，0：平台；1：授权商户；2：门店；
 * @property integer $is_show 菜单状态，0：不作为菜单；1：作为菜单。
 * @property integer $sort 排序
 * @property integer $status 状态
 * @property string $created_at 添加时间
 * @property string $updated_at 修改时间
 * @property string $deleted_at 删除时间
 */
class CsMenu extends BaseModel
{
    use SoftDeletes;

    /**
     * 验证字段
     * @var string[]
     */
    public static array $validateFields = ['id', 'pid', 'menu_name', 'menu_img', 'menu_sign', 'is_show', 'sort', 'status', ];

    public static array $relationList = [
        'csMenu' => ['*'],
    ];

    public function csMenu(): HasMany
    {
        return $this->hasMany(CsMenu::class, 'pid', 'id')
            ->orderBy('sort');
    }

    public function csMenuRecursive()
    {
        return $this->csMenu()->with('csMenuRecursive');
    }

    public function csLoginMenu()
    {
        return $this->hasMany(CsMenu::class, 'pid', 'id')
            ->where('status', 1)
            ->select(['id', 'pid', 'menu_name', 'menu_img', 'menu_sign', 'is_show'])
            ->orderBy('sort');
    }

    public function csLoginMenuRecursive()
    {
        return $this->csLoginMenu()->with('csLoginMenuRecursive');
    }

    /**
     * 列表
     * @return array
     */
    public static function getList(array $where = []): array
    {
        $orderField = request()->input('sort_field', 'id');
        $order = request()->input('sort', 'desc');
        $page = request()->input('page',1);
        $pageSize = request()->input('page_size', self::PAGE_SIZE);
        $pageSize = intval($pageSize);
        $offset = ($page - 1) * $pageSize;
        $appendWhere = static::appendWhere();
        if (!empty($appendWhere)) {
            array_push($where,...$appendWhere);
        }
        $where[] = ['pid', '=', 0];
        $itemList = static::with('csMenuRecursive')
            ->where($where)
            ->offset($offset)
            ->limit($pageSize)
            ->get();
        $counts = static::where($where)->count('id');
        return [
            'itemList' => $itemList->toArray(),
            'total' => $counts,
            'pageSize' => $pageSize,
        ];
    }
}
